const settingsData = [
    {
        id: "general",
        title: "General",
        icon: "cil-settings",
        permissionCheck: "show_settings_general",
        items: [
            { title: "Advanced", permissionCheck: "show_settings_advanced", route: "advanced-settings" },
            { title: "Schedules", permissionCheck: "show_settings_scheduled", route: "scheduled-settings" },
            { title: "Power Bi", permissionCheck: "show_settings_power_bi", route: "power-bi" },
            { title: "Company", permissionCheck: "show_settings_company", route: "company" },
            { title: "Customized Links", permissionCheck: "show_settings_customized_links", route: "customized-links" },
            { title: "Leaderboards", permissionCheck: "show_settings_leaderboards", route: "leaderboards" },
            { title: "Alerts", permissionCheck: "show_settings_alerts", route: "alerts" },
            { title: "Error Message", permissionCheck: "show_settings_error_messages", route: "error-messages" },
            { title: "Off Days", permissionCheck: "show_settings_off_days", route: "OffDays" },
            { title: "Social", permissionCheck: "show_settings_social", route: "socials" },
            { title: "Notification Center", permissionCheck: "show_settings_notification_center", route: "notificationCenters" },
            { title: "KPIs", permissionCheck: "show_settings_kpis", route: "kpis-setting" },
            { title: "Field Days KPI", permissionCheck: "show_settings_kpi_field_days", route: "field-days-kpi" },
            { title: "Post Data", permissionCheck: "show_settings_kpi_field_days", route: "post-data" },
            { title: "Policies", permissionCheck: "show_settings_Policies", route: "policies-setting" }
        ]
    },
    {
        id: "user",
        title: "User",
        icon: "cil-people",
        permissionCheck: "show_settings_user",
        items: [
            { title: "Users", permissionCheck: "show_settings_users", route: "users" },
            { title: "Roles", permissionCheck: "show_settings_roles", route: "roles" },
            { title: "Permissions", permissionCheck: "show_settings_permissions", route: "permissions" }
        ]
    },
    {
        id: "structure",
        title: "Structure",
        icon: "cil-clone",
        permissionCheck: "show_settings_structure",
        items: [
            {
                id: "line",
                title: "Lines",
                permissionCheck: "show_settings_lines",
                isButton: true,
                subitems: [
                    { title: "Countries", route: "countries" },
                    { title: "Currencies", route: "currencies" },
                    { title: "Division Types", route: "division-types" },
                    { title: "Lines Info", route: "lines" },
                    { title: "Line Giveaways", route: "giveaways" },
                    { title: "Giveaways Samples", route: "giveawaySample" }
                ]
            },
            {
                id: "product",
                title: "Products",
                permissionCheck: "show_settings_products",
                isButton: true,
                subitems: [
                    { title: "Brands", route: "brands" },
                    { title: "Families", route: "families" },
                    { title: "Classifications", route: "classifications" },
                    { title: "Manufacturers", route: "manufacturers" },
                    { title: "Product Types", route: "product-types" },
                    { title: "Price Types", route: "price-types" },
                    { title: "Presentations", route: "presentations" },
                    { title: "Messages", route: "messages" },
                    { title: "Products Info", route: "products" },
                    { title: "Samples", route: "samples" }
                ]
            },
            {
                id: "distributors",
                title: "Distributors",
                permissionCheck: "show_settings_distributors",
                isButton: true,
                subitems: [
                    { title: "Distributors Details", route: "distributors" }
                ]
            },
            {
                id: "account",
                title: "Accounts",
                permissionCheck: "show_settings_accounts",
                isButton: true,
                subitems: [
                    { title: "List Setting", route: "List Setting" },
                    { title: "Shifts", route: "shifts" },
                    { title: "Account Types", route: "account-types" },
                    { title: "Classes", route: "classes" },
                    { title: "Bricks", route: "bricks" },
                    { title: "Specialities", route: "specialities" },
                    { title: "Levels", route: "levels" },
                    { title: "Personality Types", route: "personality-types" },
                    { title: "Classifications", route: "account-classifications" },
                    { title: "Doctors", route: "doctors" },
                    { title: "Accounts Info", route: "accounts" },
                    { title: "List Types", route: "list-type" },
                    { title: "Favourite Per User", route: "fav-per-user" },
                    { title: "Segmentation", route: "profiling" },
                    { title: "Doctor Segmentation", route: "DoctorProfiling" },
                    { title: "List Management", route: "ListManagement" }
                ]
            },
            {
                id: "linked_pharmacies",
                title: "Linked Pharmacies",
                permissionCheck: "show_all_linked_pharmacies_settings",
                isButton: true,
                subitems: [
                    { title: "Linked Pharmacies Details", route: "linked-pharmacies-settings" },
                    { title: "Link Pharmacies to Accounts", route: "linked-pharmacies" }
                ]
            },
            {
                id: "positions",
                title: "Positions",
                permissionCheck: "show_settings_positions",
                isButton: true,
                subitems: [
                    { title: "Positions Details Settings", permissionCheck: "show_settings_positions_details_settings", route: "positions-details-settings" },
                    { title: "Employee Position", permissionCheck: "show_settings_employee_position", route: "employee-position" }
                ]
            }
        ]
    },
    {
        id: "dashboard",
        title: "Dashboard",
        icon: "cil-home",
        permissionCheck: "show_settings_dashboard",
        items: [
            { title: "General Settings", permissionCheck: "show_settings_dashboard_general_settings", route: "DashboardSettings" },
            { title: "Widgets Settings", permissionCheck: "show_settings_dashboard_widgets", route: "WidgetSettings" }
        ]
    },
    {
        id: "requests",
        title: "Requests",
        icon: "cil-color-border",
        permissionCheck: "show_settings_requests",
        items: [
            {
                id: "commercial",
                title: "Commercial",
                permissionCheck: "show_settings_commercial",
                isButton: true,
                subitems: [
                    { title: "Settings", permissionCheck: "show_settings_commercial_settings", route: "commercial-settings" },
                    { title: "Request Types", permissionCheck: "show_settings_request_types", route: "request-types" },
                    { title: "Commercial Cost Types", permissionCheck: "show_settings_commercial_cost_types", route: "costs" },
                    { title: "Commercial Products", permissionCheck: "show_settings_commercial_pharmacies", route: "commercial-pharmacies" },
                    { title: "Payment Methods", permissionCheck: "show_settings_payment_methods", route: "paymentMethods" },
                    { title: "Categories", permissionCheck: "show_settings_commercial_pharmacies", route: "categories" },
                    { title: "Categories Types", permissionCheck: "show_settings_commercial_pharmacies", route: "category-types" },
                    { title: "Custody", permissionCheck: "show_settings_custody", route: "custody" },
                    { title: "Commercial Tabs", permissionCheck: "show_settings_commercial-tabs", route: "commercial-tabs" }
                ]
            },
            {
                id: "expenses",
                title: "Expenses",
                permissionCheck: "show_settings_expenses",
                isButton: true,
                subitems: [
                    { title: "Expense Settings", permissionCheck: "show_settings_expense_settings", route: "expense-settings" },
                    { title: "Expense Types", permissionCheck: "show_settings_expense_types", route: "expense-types" },
                    { title: "Expense Meals", permissionCheck: "show_settings_expense_meals", route: "expense-meals" },
                    { title: "Expense Location Settings", permissionCheck: "show_settings_expense_location_settings", route: "expense-location-settings" },
                    { title: "Expense Location Prices", permissionCheck: "show_settings_expense_location_prices", route: "expense-location-prices" },
                    { title: "Expense Price Factors", permissionCheck: "show_settings_expense_price_factors", route: "expense-price-factors" },
                    { title: "Kilometers Average", permissionCheck: "show_settings_kilometers_averages", route: "kilometers-average" }
                ]
            },
            {
                id: "material",
                title: "Material",
                permissionCheck: "show_settings_material",
                isButton: true,
                subitems: [
                    { title: "Vendors", permissionCheck: "show_settings_material_vendors", route: "material_vendors" },
                    { title: "Types", permissionCheck: "show_settings_promotional_material_types", route: "promotional-material-types" },
                    { title: "Scientific Offices", permissionCheck: "show_settings_Scientific_offices", route: "scientific-offices" }
                ]
            },
            {
                id: "personal",
                title: "Personal",
                permissionCheck: "show_settings_personal",
                isButton: true,
                subitems: [
                    { title: "Personal Request Types", permissionCheck: "show_settings_personal_request_types", route: "personal-request-types" }
                ]
            },
            {
                id: "budget",
                title: "Budget",
                permissionCheck: "show_settings_budget",
                isButton: true,
                subitems: [
                    { title: "Budget Settings", permissionCheck: "show_settings_general_budget_settings", route: "budget-settings" }
                ]
            },
            {
                id: "vacations",
                title: "Vacation",
                permissionCheck: "show_settings_vacations",
                isButton: true,
                subitems: [
                    { title: "Vacation Types", permissionCheck: "show_settings_vacation_types", route: "vacation types" },
                    { title: "Vacation Settings", permissionCheck: "show_settings_vacation_settings", route: "vacation settings" },
                    { title: "Vacation Balance", permissionCheck: "show_settings_vacation_balances", route: "vacation balance" }
                ]
            },
            // Additional standalone items that aren't in subgroups
            { title: "Commercial Approvals", permissionCheck: "show_settings_commercial_approvals", route: "commercial approvals" },
            { title: "Expense Approvals", permissionCheck: "show_settings_expense_approvals", route: "Expense Approvals" },
            { title: "Vacation Approvals", permissionCheck: "show_settings_vacation_approvals", route: "vacation approvals" },
            { title: "Office Work Types", permissionCheck: "show_settings_office_work_types", route: "officework-types" },
            { title: "Public Holidays", permissionCheck: "show_settings_public_holidays", route: "public-holidays" },
            { title: "Active & Inactive Accounts", permissionCheck: "show_settings_active_inactive_accounts", route: "Active Inactive Accounts" },
            { title: "Active & Inactive Approvals", permissionCheck: "show_settings_active_inactive_approvals", route: "active-inactive-approvals" }
        ]
    },
    {
        id: "visits",
        title: "Visits",
        icon: "cilColumns",
        permissionCheck: "show_settings_visits",
        items: [
            {
                id: "plan",
                title: "Plan Settings",
                permissionCheck: "show_settings_plan_settings",
                isButton: true,
                subitems: [
                    { title: "Plan Visit Settings", route: "plan-settings" },
                    { title: "Plan Level", route: "plan-levels" },
                    { title: "User Plan Start Day", route: "start-plan-users" },
                    { title: "Plan Limit", route: "plan limit" },
                    { title: "Plan Visit Columns", route: "plan-visit-settings" }
                ]
            },
            {
                id: "actual",
                title: "Actual Settings",
                permissionCheck: "show_settings_actual_settings",
                isButton: true,
                subitems: [
                    { title: "Actual Visit Settings", route: "actual-visit-settings" },
                    { title: "Unplanned Visits Number", route: "unplanned-visits" },
                    { title: "User Actual Start Day", route: "users-actual-start-day" },
                    { title: "Under Time Per Line", route: "Under Time" },
                    { title: "Actual Visit Required Inputs", route: "actual-visit-inputs" },
                    { title: "Account Type Distance", route: "type-distances" },
                    { title: "Visit Feedbacks", route: "visit-feedbacks" },
                    { title: "Pharmacy Types", route: "pharmacy-type-visits" }
                ]
            },
            {
                id: "other",
                title: "Other Settings",
                permissionCheck: "show_settings_other_settings",
                isButton: true,
                subitems: [
                    { title: "General Other Settings", route: "general-other-settings" },
                    { title: "Call Rate", route: "call-rates" },
                    { title: "Class Frequency", route: "class-frequencies", conditionalDisplay: "class" },
                    { title: "Specialities Frequency", route: "speciality-frequencies", conditionalDisplay: "speciality" },
                    { title: "Doctor Frequency", route: "doctor-frequencies", conditionalDisplay: "doctor" },
                    { title: "Speciality Class Frequency", route: "speciality-class-frequencies", conditionalDisplay: "speciality_class" }
                ]
            }
        ]
    },
    {
        id: "trainings",
        title: "Training",
        icon: "cil-clipboard",
        permissionCheck: "show_settings_training",
        items: [
            {
                id: "coaching",
                title: "Coaching",
                permissionCheck: "show_settings_coaching",
                isButton: true,
                subitems: [
                    { title: "General Settings", permissionCheck: "show_settings_coaching_settings", route: "coachingsettings" },
                    { title: "Evaluator Settings", permissionCheck: "show_settings_evaluator_settings", route: "EvaluatorSettings" }
                ]
            },
            {
                id: "quiz",
                title: "Quiz",
                permissionCheck: "show_settings_quiz_settings",
                isButton: true,
                subitems: [
                    { title: "General Settings", permissionCheck: "show_settings_quiz_settings", route: "Quiz Settings" },
                    { title: "Categories", route: "quiz-categories" },
                    { title: "Questions", route: "quiz-questions" },
                    { title: "Create Quiz", route: "CreateQuiz" }
                ]
            }
        ]
    },
    {
        id: "sales",
        title: "Sales",
        icon: "cil-calculator",
        permissionCheck: "show_settings_sales",
        items: [
            { title: "General Sales Settings", permissionCheck: "show_settings_general_sales_settings", route: "sales-settings" },
            { title: "Sales Mapping", permissionCheck: "show_settings_mapping", route: "sales-mapping" },
            { title: "Branch Mapping", permissionCheck: "show_settings_mapping", route: "branch-mapping" },
            { title: "Overall Sales", permissionCheck: "show_settings_overall_sales", route: "sales" },
            { title: "Branch Sales", permissionCheck: "show_settings_branch_sales", route: "branch-sales" },
            { title: "Conribution", permissionCheck: "show_settings_contribution", route: "contribution" },
            { title: "Target", permissionCheck: "show_settings_target", route: "target" },
            { title: "Target Details", permissionCheck: "show_settings_target_details", route: "target-details" },
            { title: "Mapping Unified Codes", permissionCheck: "show_settings_mapping_unified_codes", route: "unified-codes" },
            { title: "Role Sales Settings", permissionCheck: "show_role_sales_settings", route: "Role Sale Settings" },
            { title: "Sales Product Weights Settings", permissionCheck: "show_sales_product_weights_settings", route: "Sales Product Weights" },
            { title: "Incentive Schema", permissionCheck: "show_sales_incentive_schema_settings", route: "Sales Incentive Schema" },
            { title: "Kpi Incentive Schema", permissionCheck: "show_sales_kpi_incentive_schema_settings", route: "Sales Kpi Incentive Schema" },
            { title: "Sales Higher Order Incentive Schema", permissionCheck: "show_sales_higher_order_incentive_schema_settings", route: "Sales Higher Order Incentive Schema" }
        ]
    },
    {
        id: "grouping",
        title: "Grouping",
        icon: "cil-calendar-check",
        permissionCheck: "show_settings_groupings",
        items: [
            { title: "Setting", permissionCheck: "show_settings_grouping_settings", route: "Grouping Settings" },
            { title: "Line", permissionCheck: "show_settings_line_grouping", route: "line-grouping" },
            { title: "Division", permissionCheck: "show_settings_division_grouping", route: "division-grouping" },
            { title: "Account Lines", permissionCheck: "show_settings_account_lines_grouping", route: "Grouping Account Lines" },
            { title: "Account Doctors", permissionCheck: "show_settings_account_doctors_grouping", route: "Grouping Account Doctors" }
        ]
    },
    {
        id: "approval",
        title: "Approval",
        icon: "cil-list",
        permissionCheck: "show_settings_approval",
        items: [
            { title: "Approval General Setting", permissionCheck: "show_settings_approval_settings", route: "approval-general-settings" },
            { title: "Approval Center", permissionCheck: "show_settings_approval_settings", route: "approval-settings" },
            { title: "Disapproval Reasons", route: "disapproval-reasons" }
        ]
    }
];

export default settingsData;