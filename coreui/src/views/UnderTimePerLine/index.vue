<template>
  <CRow>
    <CCol col="12" xl="12">
      <transition name="slide">
        <CCard>
          <CCardHeader>
            Under Time Per Line
          </CCardHeader>
          <CCardBody>
            <CButton color="primary" v-if="checkPermission('create_under_time_per_lines')"
              :to="{ name: 'CreateUnderTimePerLine' }">Create Under Time</CButton>
            <CDataTable hover striped sorter tableFilter footer itemsPerPageSelect :items="items" :fields="fields"
              :items-per-page="1000" :active-page="1" :responsive="true" pagination thead-top>
              <template slot="thead-top">
                <td style="border-top:none;"><strong>Total</strong></td>
                <td style="border-top:none;" class="text-xs-right">
                  {{ items.length }}
                </td>
              </template>

              <template #actions="{ item }">
                <td>
                  <div class="row  justify-content-center">
                    <c-button color="primary" class="btn-sm mt-2 mr-1"
                      v-if="checkPermission('show_single_under_time_per_lines')"
                      :to="{ name: 'UnderTimePerLine', params: { id: item.id } }"><c-icon
                        name="cil-magnifying-glass" /></c-button>
                    <c-button color="success" class="btn-sm mt-2 mr-1"
                      v-if="checkPermission('edit_under_time_per_lines')"
                      :to="{ name: 'EditUnderTimePerLine', params: { id: item.id } }"><i class="cil-pencil"></i><c-icon
                        name="cil-pencil" /></c-button>
                    <c-button color="danger" class="btn-sm mt-2 mr-1"
                      v-if="checkPermission('delete_under_time_per_lines')" @click="
                        $root
                          .$confirm(
                            'Delete',
                            'Do you want to delete this record?',
                            {
                              color: 'red',
                              width: 290,
                              zIndex: 200,
                            }
                          )
                          .then((confirmed) => {
                            if (confirmed) {
                              deleteUnderTimePerLine(item);
                            }
                          })
                        "><c-icon name="cil-trash" /></c-button>
                  </div>
                </td>
              </template>
            </CDataTable>
          </CCardBody>
        </CCard>
      </transition>
    </CCol>
  </CRow>
</template>

<script>

import moment from "moment";

export default {
  data() {
    return {
      items: [],
      fields: ["id", "line", "under_time", "actions"],
    };
  },
  methods: {
    removeFavouriteListPerUser(item) {
      const index = this.items.findIndex((type) => item.id == type.id);
      this.items.splice(index, 1);
    },
    deleteUnderTimePerLine(item) {
      axios
        .delete(`/api/under-time/${item.id}`)
        .then((res) => {
          this.removeFavouriteListPerUser(item);
          this.flash("Under Time Per Line Deleted Successfully");
        })
        .catch((err) => this.showErrorMessage(err));
    },
    getData() {
      this.get('/api/under-time', 'data');
    },
  },
  created() {
    this.getData();
  }
};
</script>
