<template>
  <CRow>
    <CCol col="12" lg="12">
      <CCard no-header>
        <CCardBody>
          <template slot="header"> Create Under Time</template>
          <div class="row">
            <div class="col">
              <CFormGroup>
                <template #label>
                  <strong>Line</strong>
                </template>
                <template #input>
                  <v-select v-model="under_time.line_id" :options="lines" label="name" :value="0"
                    :reduce="(line) => line.id" placeholder="Select Line" class="mt-2" />
                </template>
              </CFormGroup>
            </div>
            <div class="col" v-if="under_time.line_id">
              <c-input type="number" label="Under Time" v-model="under_time.time"></c-input>
            </div>
          </div>
        </CCardBody>
        <CCardFooter>
          <CButton color="primary" @click="store">Create</CButton>
          <CButton color="default" @click="$router.go(-1)">Back</CButton>
        </CCardFooter>
      </CCard>
    </CCol>
  </CRow>
</template>
<script>
import vSelect from "vue-select";
import "vue-select/dist/vue-select.css";
export default {
  components: {
    vSelect,
  },
  data: () => {
    return {
      under_time: {
        line_id: null,
        time: null,
      },
      lines: [],
      divisions: [],
      message: "",
    };
  },
  methods: {
    initialize() {
      axios
        .get("/api/user_actual_start_day/create")
        .then((response) => {
          this.lines = response.data.lines;
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
    store() {
      axios
        .post("/api/under-time", {
          line_id: this.under_time.line_id,
          time: this.under_time.time,
        })
        .then((response) => {
          this.flash("Under Time Created Successfully");
          this.under_time = {
            line_id: null,
            time: null,
          };
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
  },
  created() {
    this.initialize();
  },
};
</script>