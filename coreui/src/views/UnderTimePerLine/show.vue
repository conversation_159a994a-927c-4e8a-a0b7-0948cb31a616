<template>
  <CRow>
    <CCol col="12" lg="12">
      <CCard>
        <CCardHeader>
          Under Time Id:  {{ $route.params.id }}
        </CCardHeader>
        <CCardBody>
          <CTabs>
            <CTab active>
              <template slot="title">
                <CIcon class="custom_icons" name="cil-chart-pie"/> Information
              </template>
              <CDataTable
                striped
                small
                fixed
                :items="under_time_items"
                :fields="fields"
              >
                <template slot="value" slot-scope="data">
                  <strong>{{data.item.value}}</strong>
                </template>
              </CDataTable>
            </CTab>
          </CTabs>
        </CCardBody>
        <CCardFooter>
          <CButton color="primary" :to="{ name: 'under-time-per-line' }">Back</CButton>
        </CCardFooter>
        </CCard>
    </CCol>
  </CRow>
</template>

<script>
export default {
  data() {
    return {
      under_time_items: [],
      fields: [{ key: "key" }, { key: "value" }],
    };
  },
  methods: {
    initialize() {
      axios
        .get(`/api/under-time/${this.$route.params.id}`)
        .then((response) => {
          const under_time_items = Object.entries(response.data.under_time);
          this.under_time_items = under_time_items.map(([key, value]) => {
            return { key: key, value: value };
          });
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
  },
  created() {
    this.initialize();
  },
};
</script>
