<template>
  <div class="kpi-filter-container">
    <!-- Primary Filters Row -->
    <div class="filter-row primary-filters">
      <div class="filter-group">
        <label class="filter-label">
          <c-icon name="cil-layers" class="me-1"></c-icon>
          Lines
        </label>
        <div class="filter-input">
          <ejs-multiselect
            ref="linesMultiSelect"
            floatLabelType='Never'
            :enableSelectionOrder='enableSelectionOrder'
            v-model="line_id"
            :dataSource='lines'
            :fields="fields"
            :changeOnBlur="false"
            mode="CheckBox"
            :showSelectAll='true'
            selectAllText="Select All"
            unSelectAllText="Unselect All"
            placeholder="Choose lines..."
            @change="getLineData"
            cssClass="modern-multiselect"
            :enabled="true"
          >
          </ejs-multiselect>
        </div>
      </div>

      <div class="filter-group">
        <label class="filter-label">
          <c-icon name="cil-filter" class="me-1"></c-icon>
          Filter By
        </label>
        <div class="filter-input">
          <!-- Fallback to native select if Syncfusion doesn't work -->
          <select
            v-if="useFallback"
            v-model="filter"
            class="form-select modern-select"
            @change="onFilterChange"
          >
            <option value="">Choose filter type...</option>
            <option value="1">Division</option>
            <option value="2">Employee</option>
          </select>

          <ejs-dropdownlist
            v-else
            ref="filterDropdown"
            v-model="filter"
            floatLabelType='Never'
            :dataSource="[{ name: 'Division', id: 1 }, { name: 'Employee', id: 2 }]"
            placeholder='Choose filter type...'
            :fields="fields"
            cssClass="modern-dropdown"
            :enabled="true"
            @change="onFilterChange"
          >
          </ejs-dropdownlist>
        </div>
      </div>

      <div class="filter-group">
        <label class="filter-label">
          <c-icon name="cil-people" class="me-1"></c-icon>
          {{ getDataWhenFiltered.placeholder.replace('Select ', '') }}
        </label>
        <div class="filter-input">
          <ejs-multiselect
            ref="divisionsUsersMultiSelect"
            :changeOnBlur="false"
            floatLabelType='Never'
            :enableSelectionOrder='enableSelectionOrder'
            v-model="divisions_id_users_id"
            :dataSource='getDataWhenFiltered.options'
            :fields="fields"
            mode="CheckBox"
            :showSelectAll='true'
            selectAllText="Select All"
            unSelectAllText="Unselect All"
            :placeholder="getDataWhenFiltered.placeholder"
            cssClass="modern-multiselect"
            :enabled="true"
          >
          </ejs-multiselect>
        </div>
      </div>

      <div class="filter-group">
        <label class="filter-label">
          <c-icon name="cil-medical-cross" class="me-1"></c-icon>
          Type
        </label>
        <div class="filter-input">
          <ejs-dropdownlist
            ref="accountDoctorDropdown"
            v-model="accountOrDoctor"
            floatLabelType='Never'
            :dataSource="[{ name: 'Account', id: 1 }, { name: 'Doctor', id: 2 }]"
            placeholder='Select type...'
            :fields="fields"
            cssClass="modern-dropdown"
            :enabled="true"
          >
          </ejs-dropdownlist>
        </div>
      </div>
    </div>

    <!-- Secondary Filters Row -->
    <div class="filter-row secondary-filters">
      <div class="filter-group date-group">
        <label class="filter-label">
          <c-icon name="cil-calendar" class="me-1"></c-icon>
          Date Range
        </label>
        <div class="filter-input">
          <ejs-daterangepicker
            ref="dateRangePicker"
            v-model="fromToDate"
            floatLabelType='Never'
            placeholder="Select date range..."
            cssClass="modern-daterange"
            :enabled="true"
          >
          </ejs-daterangepicker>
        </div>
      </div>

      <div class="filter-group">
        <label class="filter-label">
          <c-icon name="cil-tags" class="me-1"></c-icon>
          Account Types
        </label>
        <div class="filter-input">
          <ejs-multiselect
            ref="accountTypesMultiSelect"
            :changeOnBlur="false"
            floatLabelType='Never'
            :enableSelectionOrder='enableSelectionOrder'
            v-model="type_id"
            :dataSource='types'
            :fields="fields"
            mode="CheckBox"
            :showSelectAll='true'
            selectAllText="Select All"
            unSelectAllText="Unselect All"
            placeholder="Choose account types..."
            cssClass="modern-multiselect"
            :enabled="true"
          >
          </ejs-multiselect>
        </div>
      </div>

      <div class="filter-group">
        <label class="filter-label">
          <c-icon name="cil-medical-cross" class="me-1"></c-icon>
          Specialities
        </label>
        <div class="filter-input">
          <ejs-multiselect
            ref="specialitiesMultiSelect"
            :changeOnBlur="false"
            floatLabelType='Never'
            :enableSelectionOrder='enableSelectionOrder'
            v-model="speciality_id"
            :dataSource='specialities'
            :fields="fields"
            mode="CheckBox"
            :showSelectAll='true'
            selectAllText="Select All"
            unSelectAllText="Unselect All"
            placeholder="Choose specialities..."
            cssClass="modern-multiselect"
            :enabled="true"
          >
          </ejs-multiselect>
        </div>
      </div>
    </div>

    <!-- Action Buttons -->
    <div class="filter-actions">
      <c-button
        color="primary"
        :disabled="isDisabled"
        @click="show"
        class="load-btn"
        size="lg"
      >
        <c-icon name="cil-chart-line" class="me-2"></c-icon>
        Load KPI Data
        <div v-if="isLoading" class="spinner-border spinner-border-sm ms-2" role="status">
          <span class="visually-hidden">Loading...</span>
        </div>
      </c-button>

      <c-button
        color="secondary"
        @click="resetFilters"
        class="reset-btn"
        variant="outline"
      >
        <c-icon name="cil-reload" class="me-2"></c-icon>
        Reset Filters
      </c-button>
    </div>
  </div>
</template>
<script>
import { MultiSelectComponent, DropDownListComponent } from "@syncfusion/ej2-vue-dropdowns";
import {
  AccordionComponent,
  AccordionItemDirective,
  AccordionItemsDirective,
  ToolbarComponent,
  ItemDirective,
  ItemsDirective
} from "@syncfusion/ej2-vue-navigations";
import { DateRangePickerComponent } from '@syncfusion/ej2-vue-calendars';
import { CheckBoxSelection } from '@syncfusion/ej2-dropdowns';
import { Internationalization } from "@syncfusion/ej2-base";
import moment from "moment";

let instance = new Internationalization();
let templateMapping = {
  multiSelectLines: 'multiSelectLines',
  multiSelectEmployeesDivisions: 'multiSelectEmployeesDivisions',
  datePicker: 'datePicker',
  multiSelectAccountTypes: 'multiSelectAccountTypes',
  multiSelectSpecialities: 'multiSelectSpecialities',
  dropdownFilterBy: 'dropdownFilterBy',
  dropdownSalesBy: 'dropdownAccountOrDoctor',
  load: "Load",
  reset: "Reset",
}
export default {
  name: 'ToolBarKpiFilter',
  components: {
    'ejs-daterangepicker': DateRangePickerComponent,
    'ejs-dropdownlist': DropDownListComponent,
    'ejs-multiselect': MultiSelectComponent,
    'ejs-accordion': AccordionComponent,
    'e-accordionitem': AccordionItemDirective,
    'e-accordionitems': AccordionItemsDirective,
    'ejs-toolbar': ToolbarComponent,
    'e-items': ItemsDirective,
    'e-item': ItemDirective,
  },
  emits: ["getFilterKpi", "loading"],
  provide: {
    multiselect: [CheckBoxSelection],
    dropdownlist: [],
    daterangepicker: []
  },
  data: function () {
    return {
      lines: [],
      line_id: [],
      users: [],
      user_id: [],
      types: [],
      type_id: [],
      divisions: [],
      division_id: [],
      divisions_id_users_id: [],
      specialities: [],
      speciality_id: [],
      accountOrDoctor: 2,
      filter: 1,
      enableSelectionOrder: true,
      fields: { text: 'name', value: 'id' },
      templateMapping,
      fromToDate: [
        moment().startOf("month").toDate(),
        moment().endOf("month").toDate(),
      ],
      isLoading: false,
      useFallback: false, // Flag to use native HTML elements if Syncfusion fails
    }
  },
  computed: {
    getDataWhenFiltered() {
      if (this.filter === 1)
        return {
          options: this.divisions,
          placeholder: "Select Divisions"
        }

      return {
        options: this.users,
        placeholder: "Select Employees"
      }
    },
    isDisabled() {
      return this.line_id.length === 0
    }
  },
  methods: {
    initialize() {
      console.log('Initializing KPI filter...');
      axios
        .post("/api/get-lines-with-dates", {
          from: null,
          to: null,
          data: ['lines', 'users', 'accountTypes']
        })
        .then((response) => {
          console.log('API response:', response.data);
          this.lines = response.data.data.lines || [];
          this.users = response.data.data.users ? response.data.data.users.map((user) => ({ ...user, name: user.fullname })) : [];
          this.types = response.data.data.accountTypes || [];
          this.type_id = this.types.map(item => item.id);

          console.log('Lines loaded:', this.lines.length);
          console.log('Users loaded:', this.users.length);
          console.log('Types loaded:', this.types.length);

          // Refresh components after data is loaded
          this.$nextTick(() => {
            this.refreshAllComponents();
          });
        })
        .catch((error) => {
          console.error('Error initializing filter:', error);
          this.showErrorMessage(error);
        });
    },
    refreshAllComponents() {
      // Helper method to refresh all Syncfusion components
      const components = ['linesMultiSelect', 'filterDropdown', 'divisionsUsersMultiSelect',
                         'accountDoctorDropdown', 'accountTypesMultiSelect', 'specialitiesMultiSelect', 'dateRangePicker'];

      components.forEach(componentRef => {
        try {
          if (this.$refs[componentRef] && this.$refs[componentRef].refresh) {
            this.$refs[componentRef].refresh();
          }
        } catch (error) {
          console.warn(`Failed to refresh component ${componentRef}:`, error);
          // If Syncfusion components fail, enable fallback mode
          this.useFallback = true;
        }
      });
    },
    getLineData() {
      if (this.line_id && this.line_id.length > 0) {
        axios
          .post(`/api/visitsReport`, { lines: this.line_id })
          .then((response) => {
            this.divisions = response.data.divisions;
            this.users = response.data.users.map((user) => ({ ...user, name: user.fullname }));
            this.specialities = response.data.specialities;
            this.speciality_id = this.specialities.map(item => item.id);
          })
          .catch(this.showErrorMessage);
      }
    },
    onFilterChange() {
      // Clear the divisions/users selection when filter type changes
      this.divisions_id_users_id = [];
    },
    getDateString(date) {
      return instance.formatDate(date, { format: "yyyy-M-d" });
    },
    show() {
      this.isLoading = true;
      this.$emit("loading", true);

      let visitFilter = {
        lines: this.line_id,
        filter: this.filter,
        divisions: this.filter === 1 ? this.divisions_id_users_id : [],
        users: this.filter === 2 ? this.divisions_id_users_id : [],
        type: this.accountOrDoctor,
        fromDate: this.getDateString(this.fromToDate[0]),
        toDate: this.getDateString(this.fromToDate[1]),
        accountTypes: this.type_id,
        specialities: this.speciality_id,
      };

      this.$emit("getFilterKpi", { visitFilter });

      // Reset loading state after a delay
      setTimeout(() => {
        this.isLoading = false;
      }, 2000);
    },
    resetFilters() {
      this.line_id = [];
      this.divisions_id_users_id = [];
      this.type_id = this.types.map(item => item.id);
      this.speciality_id = this.specialities.map(item => item.id);
      this.filter = 1;
      this.accountOrDoctor = 2;
      this.fromToDate = [
        moment().startOf("month").toDate(),
        moment().endOf("month").toDate(),
      ];
    },
  },
  watch: {
    filter() {
      this.divisions_id_users_id = [];
    }
  },
  created() {
    this.initialize();
  },
  mounted() {
    console.log('KPI Filter component mounted');
    // Ensure all Syncfusion components are properly initialized
    this.$nextTick(() => {
      this.refreshAllComponents();
    });
  },
}
</script>

<style>
@import "../../../../node_modules/@syncfusion/ej2-base/styles/material.css";
@import "../../../../node_modules/@syncfusion/ej2-inputs/styles/material.css";
@import "../../../../node_modules/@syncfusion/ej2-vue-dropdowns/styles/material.css";
@import "../../../../node_modules/@syncfusion/ej2-buttons/styles/material.css";
@import '../../../../node_modules/@syncfusion/ej2-popups/styles/material.css';
@import '../../../../node_modules/@syncfusion/ej2-lists/styles/material.css';
@import "../../../../node_modules/@syncfusion/ej2-vue-calendars/styles/material.css";
@import "../../../../node_modules/@syncfusion/ej2-vue-navigations/styles/material.css";

/* Modern KPI Filter Styles */
.kpi-filter-container {
  padding: 0;
  background: transparent;
}

.filter-row {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
  margin-bottom: 25px;
  align-items: end;
}

.primary-filters {
  border-bottom: 1px solid rgba(0,0,0,0.1);
  padding-bottom: 20px;
}

.secondary-filters {
  margin-bottom: 15px;
}

.filter-group {
  flex: 1;
  min-width: 200px;
  display: flex;
  flex-direction: column;
}

.date-group {
  flex: 1.5;
}

.filter-label {
  font-weight: 600;
  color: #495057;
  margin-bottom: 8px;
  font-size: 0.9rem;
  display: flex;
  align-items: center;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.filter-input {
  position: relative;
}

.filter-actions {
  display: flex;
  gap: 15px;
  justify-content: center;
  align-items: center;
  padding-top: 20px;
  border-top: 1px solid rgba(0,0,0,0.1);
}

.load-btn {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
  border: none;
  padding: 12px 30px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
  transition: all 0.3s ease;
}

.load-btn:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
}

.load-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.reset-btn {
  padding: 12px 25px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  transition: all 0.3s ease;
}

.reset-btn:hover {
  transform: translateY(-1px);
}

/* Responsive design */
@media (max-width: 1200px) {
  .filter-group {
    min-width: 180px;
  }
}

/* Fallback native select styling */
.modern-select {
  border-radius: 8px !important;
  border: 2px solid #e9ecef !important;
  transition: all 0.3s ease !important;
  background: white !important;
  padding: 10px 12px !important;
  font-size: 0.9rem !important;
}

.modern-select:hover {
  border-color: #667eea !important;
  box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.15) !important;
}

.modern-select:focus {
  border-color: #667eea !important;
  box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25) !important;
  outline: none !important;
}

@media (max-width: 768px) {
  .filter-row {
    flex-direction: column;
    gap: 15px;
  }

  .filter-group {
    min-width: 100%;
  }

  .filter-actions {
    flex-direction: column;
    gap: 10px;
  }

  .load-btn, .reset-btn {
    width: 100%;
    max-width: 300px;
  }
}
</style>

<!-- Global styles for Syncfusion components -->
<style>
/* Modern Syncfusion component styling */
.modern-multiselect.e-multiselect.e-input-group,
.modern-dropdown.e-dropdownlist.e-input-group,
.modern-daterange.e-daterangepicker.e-input-group {
  border-radius: 8px !important;
  border: 2px solid #e9ecef !important;
  transition: all 0.3s ease !important;
  background: white !important;
}

.modern-multiselect.e-multiselect.e-input-group:hover,
.modern-dropdown.e-dropdownlist.e-input-group:hover,
.modern-daterange.e-daterangepicker.e-input-group:hover {
  border-color: #667eea !important;
  box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.15) !important;
}

.modern-multiselect.e-multiselect.e-input-group.e-input-focus,
.modern-dropdown.e-dropdownlist.e-input-group.e-input-focus,
.modern-daterange.e-daterangepicker.e-input-group.e-input-focus {
  border-color: #667eea !important;
  box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25) !important;
}

.modern-multiselect .e-multi-select-wrapper,
.modern-dropdown .e-input-inner,
.modern-daterange .e-input-inner {
  padding: 10px 12px !important;
  font-size: 0.9rem !important;
}

/* Dropdown popup styling */
.e-popup.e-multiselect-popup,
.e-popup.e-dropdownlist-popup {
  border-radius: 8px !important;
  box-shadow: 0 10px 40px rgba(0,0,0,0.15) !important;
  border: 1px solid #e9ecef !important;
}

/* Checkbox styling in multiselect */
.e-multiselect-popup .e-list-item .e-frame {
  border-radius: 4px !important;
}

.e-multiselect-popup .e-list-item .e-frame.e-check {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
  border-color: #667eea !important;
}

/* Selected item chips styling */
.modern-multiselect .e-chips {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
  color: white !important;
  border-radius: 15px !important;
  padding: 4px 10px !important;
  margin: 2px !important;
  font-size: 0.8rem !important;
}

.modern-multiselect .e-chips .e-chips-close {
  color: white !important;
}

/* Date range picker styling */
.modern-daterange .e-input-group-icon {
  color: #667eea !important;
}

/* Placeholder styling */
.modern-multiselect .e-multi-select-wrapper .e-placeholder,
.modern-dropdown .e-input-inner::placeholder,
.modern-daterange .e-input-inner::placeholder {
  color: #6c757d !important;
  font-style: italic !important;
}
</style>
