<template>
  <CRow>
    <CCol col="12" xl="12">
      <transition name="slide">
        <CCard>
          <CCardHeader> vacations </CCardHeader>
          <CCardBody>
            <c-button color="primary" v-if="checkPermission('create_vacations')" :to="{ name: 'CreateVacation' }">Create
              Vacation</c-button>
            <CDropdown placement="bottom-end" toggler-text="Tools" color="primary" class="d-inline-block float-right">
              <CDropdownItem v-if="checkPermission('export_xlsx_vacations')" @click="exportVacation()">Export to Excel
              </CDropdownItem>
              <CDropdownItem v-if="checkPermission('export_csv_vacations')" @click="exportCSV()">Export to CSV
              </CDropdownItem>
              <CDropdownItem v-if="checkPermission('export_pdf_vacations')" @click="exportVacationPDF()">Export to PDF
              </CDropdownItem>
              <CDropdownItem v-if="checkPermission('export_email_vacations')" @click="sendModal = true">Send to Mail
              </CDropdownItem>
            </CDropdown>
            <CModal title="Compose" color="success" :show.sync="sendModal">
              <template>
                <div class="form-group">
                  <vue-tags-input v-model="email" :tags="emails" name="email[]" :validation="validation"
                    placeholder="To" :add-on-key="addOnKey" @tags-changed="(newEmails) => (emails = newEmails)" />
                </div>
              </template>
              <CTextarea label="Message:" type="text" name="text" v-model="text" placeholder="Message"></CTextarea>
              <template #footer>
                <CButton class="text-white" @click="sendModal = false" color="danger">Discard</CButton>
                <CButton class="text-white" @click="sendVacationMail()" color="success">Send</CButton>
              </template>
            </CModal>
            <CDataTable hover striped sorter footer itemsPerPageSelect :items="items" :fields="fields"
              :items-per-page="1000" :active-page="1" :responsive="true" thead-top>
              <template slot="cleaner">
                <label class="mfe-2">Filter: </label>
                <c-input v-model="search" placeholder="type string..." type="text" />
              </template>
              <template slot="thead-top">
                <td style="border-top: none"><strong>Total</strong></td>
                <td style="border-top: none" class="text-xs-right">
                  {{ totalData }}
                </td>
              </template>
              <template #from="{ item }">
                <td>
                  {{ crmDateFormat(item.from, 'YYYY-MM-DD') }}
                </td>
              </template>
              <template #to="{ item }">
                <td>
                  {{ crmDateFormat(item.to, 'YYYY-MM-DD') }}
                </td>
              </template>
              <template #notes="{ item }">
                <td v-if="item.notes">{{ item.notes.substring(0, 15) }}...</td>
                <td v-else-if="!item.notes"></td>
              </template>
              <template #status="{ item }">
                <td>
                  <strong v-if="item.status == 1" style="color: green">Approved</strong>
                  <strong v-if="item.status == 0" style="color: red">Dispproved</strong>
                  <strong v-if="item.status == null" style="color: blue">Pending</strong>
                </td>
              </template>
              <template #approvals="{ item }">
                <td>
                  <CButton color="warning" variant="outline" square size="sm" @click="showApprovalDetails(item)">
                    show
                  </CButton>
                </td>
              </template>
              <template #actions="{ item }">
                <td>
                  <div class="row justify-content-center">
                    <c-button color="warning" class="btn-sm mt-2 mr-1"
                      v-if="checkPermission('reset_vacation_approvals')" @click="
                        $root
                          .$confirm(
                            'Reset',
                            'Do you want to reset approvals for this record?',
                            {
                              color: 'warning',
                              width: 290,
                              zIndex: 200,
                            }
                          )
                          .then((confirmed) => {
                            if (confirmed) {
                              resetApprovals(item);
                            }
                          })
                        "><c-icon name="cil-loop-circular" /></c-button>
                    <c-button color="primary" class="btn-sm mt-2 mr-1" v-if="checkPermission('show_single_vacations')"
                      :to="{ name: 'vacation', params: { id: item.id } }">
                      <CIcon name="cil-magnifying-glass" />
                    </c-button>
                    <c-button style="background-color: blueviolet;" class="btn-sm mt-2 mr-1 text-white"
                      v-if="checkPermission('show_single_vacations')"
                      :to="{ name: 'vacationArabic', params: { id: item.id } }">
                      <CIcon name="cil-print" />
                    </c-button>
                    <c-button color="success" class="btn-sm mt-2 mr-1" v-if="checkPermission('edit_vacations') &&
                      item.status == null &&
                      setting == 'Before Approval' &&
                      item.from >= new Date().toISOString().slice(0, 10) + ' 00:00:00' &&
                      !checkPermission('all_permissions')
                    " :to="{ name: 'EditVacation', params: { id: item.id } }">
                      <CIcon name="cil-pencil" />
                    </c-button>
                    <c-button color="success" class="btn-sm mt-2 mr-1" v-if="checkPermission('edit_vacations') &&
                      setting == 'All' &&
                      !checkPermission('all_permissions')
                    " :to="{ name: 'EditVacation', params: { id: item.id } }">
                      <CIcon name="cil-pencil" />
                    </c-button>
                    <c-button color="success" class="btn-sm mt-2 mr-1" v-if="checkPermission('edit_vacations') && (item.status == 1 || item.status == null) &&
                      setting == 'After Approval' &&
                      item.from > new Date().toISOString().slice(0, 10) + ' 00:00:00' &&
                      !checkPermission('all_permissions')
                    " :to="{ name: 'EditVacation', params: { id: item.id } }">
                      <CIcon name="cil-pencil" />
                    </c-button>
                    <c-button color="success" class="btn-sm mt-2 mr-1" v-if="checkPermission('all_permissions')"
                      :to="{ name: 'EditVacation', params: { id: item.id } }">
                      <CIcon name="cil-pencil" />
                    </c-button>
                    <c-button color="danger" class="btn-sm mt-2 mr-1" v-if="checkPermission('delete_vacations') &&
                      item.status == null &&
                      setting == 'Before Approval' &&
                      item.from > new Date().toISOString().slice(0, 10) + ' 00:00:00' &&
                      !checkPermission('all_permissions')" @click="
                        $root
                          .$confirm(
                            'Delete',
                            'Do you want to delete this record?',
                            {
                              color: 'red',
                              width: 290,
                              zIndex: 200,
                            }
                          )
                          .then((confirmed) => {
                            if (confirmed) {
                              deleteVacation(item);
                            }
                          })
                        "><c-icon name="cil-trash" /></c-button>

                    <c-button color="danger" class="btn-sm mt-2 mr-1" v-if="checkPermission('delete_vacations') &&
                      (item.status == 1 || item.status == null) &&
                      setting == 'After Approval' &&
                      item.from > new Date().toISOString().slice(0, 10) + ' 00:00:00' &&
                      !checkPermission('all_permissions')" @click="
                        $root
                          .$confirm(
                            'Delete',
                            'Do you want to delete this record?',
                            {
                              color: 'red',
                              width: 290,
                              zIndex: 200,
                            }
                          )
                          .then((confirmed) => {
                            if (confirmed) {
                              deleteVacation(item);
                            }
                          })
                        "><c-icon name="cil-trash" /></c-button>


                    <c-button color="danger" class="btn-sm mt-2 mr-1" v-if="checkPermission('all_permissions')" @click="
                      $root
                        .$confirm(
                          'Delete',
                          'Do you want to delete this record?',
                          {
                            color: 'red',
                            width: 290,
                            zIndex: 200,
                          }
                        )
                        .then((confirmed) => {
                          if (confirmed) {
                            deleteVacation(item);
                          }
                        })
                      "><c-icon name="cil-trash" /></c-button>
                    <c-button color="danger" class="btn-sm mt-2 mr-1" v-if="checkPermission('delete_vacations') &&
                      setting == 'All' &&
                      !checkPermission('all_permissions')" @click="
                        $root
                          .$confirm(
                            'Delete',
                            'Do you want to delete this record?',
                            {
                              color: 'red',
                              width: 290,
                              zIndex: 200,
                            }
                          )
                          .then((confirmed) => {
                            if (confirmed) {
                              deleteVacation(item);
                            }
                          })
                        "><c-icon name="cil-trash" /></c-button>
                  </div>
                </td>
              </template>
            </CDataTable>
            <c-pagination v-if="items.length != 0" :activePage.sync="page" @update:activePage="getData()"
              :pages="total" />
          </CCardBody>
        </CCard>
      </transition>
    </CCol>
  </CRow>
</template>

<script>
import moment from "moment";
import VueTagsInput from "@johmun/vue-tags-input";

export default {
  components: {
    VueTagsInput,
  },
  data() {
    return {
      items: [],
      successModal: false,
      total: 0,
      fields: [
        "id",
        "user",
        "from",
        "to",
        "type",
        "shift",
        "status",
        "notes",
        "approvals",
        "actions",
      ],
      details_fields: ["approved_by", "position", "status", "reason", "date"],
      details: [],
      addOnKey: [13, 32, ":", ";"],
      sendModal: false,
      validation: [
        {
          classes: "email",
          rule: /^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,24}))$/,
        },
      ],
      email: "",
      emails: [],
      text: "",
      collapseDuration: 0,
      page: 1,
      total: 0,
      totalData: 0,
      previousTimeout: null,
      search: null,
      setting: null,
    };
  },
  methods: {
    resetApprovals(item) {
      axios
        .post("/api/vacation-approval-reset", item)
        .then((response) => {
          this.flash("Vacation Approvals reset Successfully");
          this.getData();
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
    showApprovalDetails(item) {
      axios
        .post("/api/vacation-approval-data", item)
        .then((response) => {
          const approvals = response.data.data;
          this.$root.$table("Vacation Approval", approvals);
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
    toggleDetails(item, index) {
      this.$set(this.items[index], "_toggled", !item._toggled);
      this.collapseDuration = 300;
      this.$nextTick(() => {
        this.collapseDuration = 0;
      });
    },
    removeVacation(item) {
      const index = this.items.findIndex((type) => item.id == type.id);
      this.items.splice(index, 1);
    },
    deleteVacation(item) {
      axios
        .delete(`/api/vacations/${item.id}`)
        .then((res) => {
          this.removeVacation(item);
          this.flash("Vacation Deleted Successfully");
        })
        .catch((err) => this.showErrorMessage(err));
    },
    exportVacation() {
      this.exportFile("/api/exportvacations", "vacations.xlsx");
    },
    exportCSV() {
      this.exportFile("/api/exportvacations-csv", "vacations.csv");
    },
    exportVacationPDF() {
      this.exportFile("/api/exportvacations-pdf", "vacations.pdf");
    },
    sendVacationMail() {
      const formData = {
        emails: JSON.stringify(this.emails),
        text: this.text,
      };
      this.sendMail("/api/send-mailvacations", formData);
      this.successModal = false;
    },
    timeoutClear() {
      if (this.previousTimeout) {
        clearTimeout(this.previousTimeout);
        this.previousTimeout = null;
      }
    },
    getData() {
      axios
        .post("/api/vacations/index?page=" + this.page, {
          query: this.search,
        })
        .then((res) => {
          this.items = res.data.data.data;
          this.total = res.data.data.last_page;
          this.totalData = res.data.data.total;
        })
        .catch((err) => {
          this.showErrorMessage(err);
        });
    },
    getSetting() {
      axios
        .get("/api/vacation-setting")
        .then((response) => {
          this.setting = response.data.data;
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    }

  },
  created() {
    this.getData();
    this.getSetting();
  },
  watch: {
    search() {
      this.timeoutClear();
      const id = setTimeout(() => this.getData(), 500);
      this.previousTimeout = id;
    },
  },
};
</script>
