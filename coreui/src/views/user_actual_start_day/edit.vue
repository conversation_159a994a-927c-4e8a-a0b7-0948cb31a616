<template>
  <CRow>
    <CCol col="12" lg="12">
      <CCard no-header>
        <CCardBody>
          <CForm>
            <template slot="header">
              Edit User Actual Start Day id: {{ $route.params.id }}
            </template>
            <div class="row">
              <div class="col">
                <CFormGroup>
                  <template #label>
                    <strong>Line</strong>
                  </template>
                  <template #input>
                    <v-select
                      v-model="user_actual_start_day_data.line_id"
                      :options="lines"
                      label="name"
                      :value="0"
                      :reduce="(line) => line.id"
                      placeholder="Select Line"
                      class="mt-2"
                      @input="getLineDivisions()"
                      disabled
                    />
                  </template>
                </CFormGroup>
              </div>
              <div class="col" v-if="user_actual_start_day_data.user_id == 0">
                <CFormGroup>
                  <template #label> Employee </template>
                  <template #input>
                    <v-select
                      disabled
                      v-model="user_actual_start_day_data.user_id"
                      :options="users"
                      label="name"
                      :value="0"
                      :reduce="(user) => user.id"
                      placeholder="Select Employee"
                      class="mt-2"
                    />
                  </template>
                </CFormGroup>
              </div>
              <div
                class="col"
                v-else-if="user_actual_start_day_data.user_id != 0"
              >
                <CFormGroup>
                  <template #label> User </template>
                  <template #input>
                    <v-select
                      v-model="user_actual_start_day_data.user_id"
                      :options="users"
                      label="name"
                      :value="0"
                      :reduce="(user) => user.id"
                      placeholder="Select option"
                      class="mt-2"
                    />
                  </template>
                </CFormGroup>
              </div>
              <div class="col">
                <CInput
                  label="Date"
                  type="date"
                  placeholder="Date"
                  v-model="user_actual_start_day_data.date"
                ></CInput>
              </div>
            </div>
          </CForm>
        </CCardBody>
        <CCardFooter>
          <CButton color="primary" @click="update()">Update</CButton>
          <CButton color="primary" @click="$router.go(-1)">Back</CButton>
        </CCardFooter>
      </CCard>
    </CCol>
  </CRow>
</template>

<script>
import moment from "moment";
import vSelect from "vue-select";
import "vue-select/dist/vue-select.css";
export default {
  components: {
    vSelect,
  },
  props: {
    caption: {
      type: String,
      default: "User Plan Start Day id",
    },
  },
  data() {
    return {
      user_actual_start_day_data: {
        id: 0,
        line_id: "",
        user_id: "",
        date: "",
      },
      lines: [],
      users: [],
      message: "",
    };
  },
  methods: {
    initialize() {
      axios
        .get(`/api/user_actual_start_day/${this.$route.params.id}/edit`)
        .then((response) => {
          this.lines = response.data.lines;
          this.users = response.data.users;
          this.user_actual_start_day_data.id =
            response.data.user_actual_start_day_data.id;
          this.user_actual_start_day_data.line_id =
            response.data.user_actual_start_day_data.line_id;
          this.user_actual_start_day_data.user_id =
            response.data.user_actual_start_day_data.user_id;
          this.user_actual_start_day_data.date = this.edit_date_format(
            response.data.user_actual_start_day_data.date
          );
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
    format_date(value) {
      let date_format = localStorage.getItem("date_format");
      if (value) {
        return moment(String(value)).format(date_format);
      }
    },
    edit_date_format(value) {
      if (value) {
        return moment(String(value)).format("YYYY-MM-DD");
      }
    },
    update() {
      axios
        .put(`/api/user_actual_start_day/${this.$route.params.id}`, {
          id: this.user_actual_start_day_data.id,
          line_id: this.user_actual_start_day_data.line_id,
          user_id: this.user_actual_start_day_data.user_id,
          date: this.crmDateFormat(this.user_actual_start_day_data.date),
        })
        .then((response) => {
          if (response.data.message == "success") {
            this.flash("Start Actual Visit Updated Successfully");
          } else {
            this.message = response.data.message;
            this.flash(this.message);
          }
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
  },
  created() {
    this.initialize();
  },
};
</script>
