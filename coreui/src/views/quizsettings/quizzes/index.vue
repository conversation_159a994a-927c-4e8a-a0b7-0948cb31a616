<template>
  <c-row>
    <c-col col="12" xl="12">
      <transition name="slide">
        <c-card>
          <c-card-header>Quizzes </c-card-header>
          <c-card-body>
            <c-button color="primary" v-if="checkPermission('create_quiz')" :to="{ name: 'CreateQuiz' }">Create New
              Quiz</c-button>
            <c-data-table hover striped sorter tableFilter footer itemsPerPageSelect :items="items" :fields="fields"
              :items-per-page="1000" :active-page="1" :responsive="true" pagination thead-top>
              <template slot="thead-top">
                <td style="border-top: none"><strong>Total</strong></td>
                <td style="border-top: none" class="text-xs-right">
                  {{ items.length }}
                </td>
              </template>
              <!-- <template #name="{ item }">

              </template> -->

              <template #actions="{ item }">
                <td>
                  <div class="row justify-content-center">


                    <c-button color="primary" class="btn-sm mt-2 mr-1" v-if="checkPermission('show_single_quiz')"
                      :to="{ name: 'ShowQuiz', params: { id: item.id } }"><c-icon
                        name="cil-magnifying-glass" /></c-button>
                    <c-button color="danger" class="btn-sm mt-2 mr-1" v-if="checkPermission('delete_quiz')" @click="
                      $root
                        .$confirm('Delete', 'Do you want to delete this record?', {
                          color: 'red',
                          width: 290,
                          zIndex: 200,
                        })
                        .then((confirmed) => {
                          if (confirmed) {
                            deleteQuiz(item);
                          }
                        })
                      "><c-icon name="cil-trash" /></c-button>
                  </div>
                </td>
              </template>
            </c-data-table>
          </c-card-body>
        </c-card>
      </transition>
    </c-col>
  </c-row>
</template>
<script>
import VueTagsInput from "@johmun/vue-tags-input";

export default {
  components: {
    VueTagsInput,
  },
  data() {
    return {

      items: [],
      fields: ["quiz_id", "quiz_name", 'line', "start_date","start_time", "end_date" , "end_time", "time", "actions"],
    };
  },
  methods: {
    initialize() {
      axios
        .get("/api/quiz")
        .then((response) => {
          this.items = response.data.data;
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
    removeQuiz(item) {
      const index = this.items.findIndex(type => item.id == type.id)
      this.items.splice(index, 1)
    },
    deleteQuiz(item) {
      axios
        .delete(`/api/delete-quiz/${item.quiz_id}`)
        .then((res) => {
          this.removeQuiz(item);
          this.flash("Quiz Deleted Successfully");
        })
        .catch((err) => this.showErrorMessage(err));
    },
    
  },
  created() {
    this.initialize();
  },
};
</script>