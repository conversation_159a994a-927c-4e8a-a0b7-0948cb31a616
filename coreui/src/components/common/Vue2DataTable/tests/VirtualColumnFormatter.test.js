/**
 * Test suite for Virtual Column Formatter feature
 */

import { mount } from '@vue/test-utils'
import Vue2DataTable from '../components/core/Vue2DataTable.vue'

describe('Vue2DataTable Virtual Column Formatters', () => {
  const sampleData = [
    {
      id: 1,
      first_name: '<PERSON>',
      last_name: '<PERSON><PERSON>',
      email: '<EMAIL>',
      age: 30,
      salary: 75000,
      active: true
    },
    {
      id: 2,
      first_name: '<PERSON>',
      last_name: '<PERSON>',
      email: '<EMAIL>',
      age: 28,
      salary: 82000,
      active: false
    }
  ]

  const basicColumns = [
    {
      key: 'id',
      label: 'ID'
    },
    {
      key: 'full_name',
      label: 'Full Name',
      virtual: true,
      formatter: (item) => `${item.first_name} ${item.last_name}`
    },
    {
      key: 'email',
      label: 'Email'
    }
  ]

  const advancedColumns = [
    {
      key: 'id',
      label: 'ID'
    },
    {
      key: 'employee_info',
      label: 'Employee Info',
      virtual: true,
      formatter: (item) => {
        const status = item.active ? 'Active' : 'Inactive'
        return `${item.first_name} ${item.last_name} (${status})`
      }
    },
    {
      key: 'salary_formatted',
      label: 'Salary',
      virtual: true,
      formatter: (item) => {
        return new Intl.NumberFormat('en-US', {
          style: 'currency',
          currency: 'USD'
        }).format(item.salary)
      },
      type: 'number'
    }
  ]

  describe('Basic Virtual Column Functionality', () => {
    let wrapper

    beforeEach(() => {
      wrapper = mount(Vue2DataTable, {
        propsData: {
          columns: basicColumns,
          dataSource: sampleData
        }
      })
    })

    afterEach(() => {
      wrapper.destroy()
    })

    test('should render virtual columns correctly', async () => {
      await wrapper.vm.$nextTick()
      
      // Check if virtual column is processed correctly
      const processedColumns = wrapper.vm.processedColumns
      const virtualColumn = processedColumns.find(col => col.key === 'full_name')
      
      expect(virtualColumn).toBeDefined()
      expect(virtualColumn.virtual).toBe(true)
      expect(virtualColumn.formatter).toBeInstanceOf(Function)
    })

    test('should display formatted values in virtual columns', async () => {
      await wrapper.vm.$nextTick()
      
      // Find table rows
      const tableRows = wrapper.findAll('tr')
      
      // Check if the virtual column displays the formatted value
      // Note: This test assumes the table renders the data
      expect(tableRows.length).toBeGreaterThan(0)
    })

    test('virtual column formatter should receive correct item data', () => {
      const virtualColumn = basicColumns.find(col => col.virtual)
      const formattedValue = virtualColumn.formatter(sampleData[0])
      
      expect(formattedValue).toBe('John Doe')
    })
  })

  describe('Advanced Virtual Column Features', () => {
    let wrapper

    beforeEach(() => {
      wrapper = mount(Vue2DataTable, {
        propsData: {
          columns: advancedColumns,
          dataSource: sampleData
        }
      })
    })

    afterEach(() => {
      wrapper.destroy()
    })

    test('should handle complex formatter functions', () => {
      const employeeInfoColumn = advancedColumns.find(col => col.key === 'employee_info')
      const formattedValue = employeeInfoColumn.formatter(sampleData[0])
      
      expect(formattedValue).toBe('John Doe (Active)')
    })

    test('should handle currency formatting', () => {
      const salaryColumn = advancedColumns.find(col => col.key === 'salary_formatted')
      const formattedValue = salaryColumn.formatter(sampleData[0])
      
      expect(formattedValue).toMatch(/\$75,000\.00/)
    })

    test('should preserve column type for virtual columns', async () => {
      await wrapper.vm.$nextTick()
      
      const processedColumns = wrapper.vm.processedColumns
      const salaryColumn = processedColumns.find(col => col.key === 'salary_formatted')
      
      expect(salaryColumn.type).toBe('number')
    })
  })

  describe('Virtual Column Integration', () => {
    let wrapper

    beforeEach(() => {
      wrapper = mount(Vue2DataTable, {
        propsData: {
          columns: basicColumns,
          dataSource: sampleData,
          searchable: true,
          sortable: true
        }
      })
    })

    afterEach(() => {
      wrapper.destroy()
    })

    test('should include virtual columns in searchable columns', async () => {
      await wrapper.vm.$nextTick()
      
      const searchableColumns = wrapper.vm.searchableColumns
      const virtualColumn = searchableColumns.find(col => 
        (typeof col === 'object' ? col.key : col) === 'full_name'
      )
      
      expect(virtualColumn).toBeDefined()
    })

    test('should handle virtual column search correctly', async () => {
      await wrapper.vm.$nextTick()
      
      // Set search term that should match virtual column content
      wrapper.setData({ internalSearchTerm: 'John Doe' })
      await wrapper.vm.$nextTick()
      
      // Check if search results include the item
      const filteredItems = wrapper.vm.filteredItems
      expect(filteredItems.length).toBeGreaterThan(0)
      expect(filteredItems[0].first_name).toBe('John')
    })

    test('should support sorting on virtual columns', async () => {
      await wrapper.vm.$nextTick()
      
      // Trigger sort on virtual column
      wrapper.vm.sortBy('full_name')
      await wrapper.vm.$nextTick()
      
      expect(wrapper.vm.sortColumn).toBe('full_name')
    })
  })

  describe('Error Handling', () => {
    test('should handle formatter errors gracefully', () => {
      const errorColumns = [
        {
          key: 'error_column',
          label: 'Error Column',
          virtual: true,
          formatter: (item) => {
            // This should not crash the component
            return item.nonexistent.property
          }
        }
      ]

      expect(() => {
        mount(Vue2DataTable, {
          propsData: {
            columns: errorColumns,
            dataSource: sampleData
          }
        })
      }).not.toThrow()
    })

    test('should handle missing formatter function', async () => {
      const invalidColumns = [
        {
          key: 'invalid_virtual',
          label: 'Invalid Virtual',
          virtual: true
          // Missing formatter function
        }
      ]

      const wrapper = mount(Vue2DataTable, {
        propsData: {
          columns: invalidColumns,
          dataSource: sampleData
        }
      })

      await wrapper.vm.$nextTick()
      
      // Should not crash, should handle gracefully
      expect(wrapper.exists()).toBe(true)
      
      wrapper.destroy()
    })
  })

  describe('Performance Considerations', () => {
    test('should call formatter function only when needed', () => {
      const mockFormatter = jest.fn((item) => `${item.first_name} ${item.last_name}`)
      
      const performanceColumns = [
        {
          key: 'full_name',
          label: 'Full Name',
          virtual: true,
          formatter: mockFormatter
        }
      ]

      const wrapper = mount(Vue2DataTable, {
        propsData: {
          columns: performanceColumns,
          dataSource: sampleData
        }
      })

      // Formatter should be called for each visible item
      expect(mockFormatter).toHaveBeenCalled()
      
      wrapper.destroy()
    })
  })

  describe('Backward Compatibility', () => {
    test('should work with regular columns alongside virtual columns', async () => {
      const mixedColumns = [
        {
          key: 'id',
          label: 'ID'
        },
        {
          key: 'full_name',
          label: 'Full Name',
          virtual: true,
          formatter: (item) => `${item.first_name} ${item.last_name}`
        },
        {
          key: 'email',
          label: 'Email'
        }
      ]

      const wrapper = mount(Vue2DataTable, {
        propsData: {
          columns: mixedColumns,
          dataSource: sampleData
        }
      })

      await wrapper.vm.$nextTick()
      
      const processedColumns = wrapper.vm.processedColumns
      
      // Should have both regular and virtual columns
      expect(processedColumns.length).toBe(mixedColumns.length)
      
      // Regular columns should not have virtual flag
      const regularColumn = processedColumns.find(col => col.key === 'id')
      expect(regularColumn.virtual).toBeFalsy()
      
      // Virtual columns should have virtual flag
      const virtualColumn = processedColumns.find(col => col.key === 'full_name')
      expect(virtualColumn.virtual).toBe(true)
      
      wrapper.destroy()
    })
  })
})
