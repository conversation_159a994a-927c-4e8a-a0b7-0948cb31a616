<template>
  <div class="scroll-test-container">
    <h2>Vue2DataTable Scroll Sensitivity & Horizontal Scrolling Test</h2>

    <div class="info-banner">
      <strong>Horizontal Scrolling Test:</strong> This table has {{ totalColumnWidth }}px total width with {{ testColumns.length }} columns.
      If your screen is narrower than this, you should see a horizontal scrollbar at the bottom of the table.
    </div>
    
    <!-- Scroll Controls -->
    <div class="scroll-controls">
      <div class="control-group">
        <label>
          Scroll Sensitivity: {{ scrollSensitivity }}
          <input 
            v-model.number="scrollSensitivity" 
            type="range" 
            min="0.1" 
            max="2.0" 
            step="0.1"
          />
        </label>
        <label>
          Wheel Multiplier: {{ wheelScrollMultiplier }}
          <input 
            v-model.number="wheelScrollMultiplier" 
            type="range" 
            min="0.1" 
            max="2.0" 
            step="0.1"
          />
        </label>
        <label>
          <input v-model="smoothScrollEnabled" type="checkbox" />
          Smooth Scrolling
        </label>
      </div>
    </div>

    <!-- Instructions -->
    <div class="instructions">
      <h3>Test Instructions:</h3>
      <ul>
        <li>Use mouse wheel to scroll through the table (vertical scrolling)</li>
        <li><strong>Test horizontal scrolling by scrolling left/right when table is wider than container</strong></li>
        <li>Adjust scroll sensitivity (lower = less sensitive)</li>
        <li>Adjust wheel multiplier (lower = slower scrolling)</li>
        <li>Toggle smooth scrolling on/off</li>
        <li>Notice how the scrolling behavior changes</li>
        <li><strong>Check that horizontal scrollbar is visible at the bottom when needed</strong></li>
      </ul>
    </div>

    <!-- Vue2DataTable with Large Dataset -->
    <Vue2DataTable
      :columns="testColumns"
      :data-source="testData"
      :virtual-scroll-enabled="true"
      :scroll-sensitivity="scrollSensitivity"
      :smooth-scroll-enabled="smoothScrollEnabled"
      :wheel-scroll-multiplier="wheelScrollMultiplier"
      :row-height="50"
      :show-search="true"
      :show-pagination="false"
      :show-total-bar="true"
      search-placeholder="Search test data..."
    />
  </div>
</template>

<script>
import Vue2DataTable from './index.js'

export default {
  name: 'Vue2DataTableScrollTest',
  
  components: {
    Vue2DataTable
  },

  data() {
    return {
      // Scroll sensitivity controls
      scrollSensitivity: 0.3,
      smoothScrollEnabled: true,
      wheelScrollMultiplier: 0.5,
      
      // Test data
      testData: [],
      testColumns: [
        {
          key: 'id',
          label: 'ID',
          sortable: true,
          width: '80px',
          type: 'number'
        },
        {
          key: 'name',
          label: 'Name',
          sortable: true,
          width: '200px'
        },
        {
          key: 'email',
          label: 'Email',
          sortable: true,
          width: '250px'
        },
        {
          key: 'department',
          label: 'Department',
          sortable: true,
          width: '150px'
        },
        {
          key: 'salary',
          label: 'Salary',
          sortable: true,
          width: '120px',
          type: 'number',
          align: 'right'
        },
        {
          key: 'joinDate',
          label: 'Join Date',
          sortable: true,
          width: '150px',
          type: 'date'
        },
        // Additional columns to force horizontal scrolling
        {
          key: 'position',
          label: 'Position',
          sortable: true,
          width: '180px'
        },
        {
          key: 'manager',
          label: 'Manager',
          sortable: true,
          width: '200px'
        },
        {
          key: 'location',
          label: 'Location',
          sortable: true,
          width: '150px'
        },
        {
          key: 'phone',
          label: 'Phone Number',
          sortable: true,
          width: '150px'
        },
        {
          key: 'status',
          label: 'Status',
          sortable: true,
          width: '120px'
        },
        {
          key: 'experience',
          label: 'Experience (Years)',
          sortable: true,
          width: '150px',
          type: 'number',
          align: 'center'
        },
        {
          key: 'skills',
          label: 'Skills',
          sortable: false,
          width: '300px'
        },
        {
          key: 'notes',
          label: 'Notes',
          sortable: false,
          width: '250px'
        }
      ]
    }
  },

  computed: {
    /**
     * Calculate total width of all columns
     */
    totalColumnWidth() {
      return this.testColumns.reduce((total, column) => {
        const width = parseInt(column.width) || 150
        return total + width
      }, 0)
    }
  },

  created() {
    this.generateTestData()
  },

  methods: {
    /**
     * Generate large test dataset
     */
    generateTestData() {
      const departments = ['Engineering', 'Marketing', 'Sales', 'HR', 'Finance', 'Operations']
      const positions = ['Senior Developer', 'Junior Developer', 'Manager', 'Director', 'Analyst', 'Coordinator', 'Specialist']
      const managers = ['John Smith', 'Sarah Johnson', 'Mike Davis', 'Lisa Wilson', 'David Brown', 'Emma Taylor']
      const locations = ['New York', 'San Francisco', 'Chicago', 'Austin', 'Seattle', 'Boston', 'Remote']
      const statuses = ['Active', 'On Leave', 'Remote', 'Part-time', 'Contract']
      const skillSets = [
        'JavaScript, React, Node.js',
        'Python, Django, PostgreSQL',
        'Java, Spring, MySQL',
        'C#, .NET, SQL Server',
        'PHP, Laravel, MongoDB',
        'Go, Docker, Kubernetes',
        'Project Management, Agile, Scrum',
        'Data Analysis, Excel, Tableau'
      ]
      const count = 5000 // Large dataset to test scrolling

      this.testData = Array.from({ length: count }, (_, index) => ({
        id: index + 1,
        name: `Employee ${index + 1}`,
        email: `employee${index + 1}@company.com`,
        department: departments[Math.floor(Math.random() * departments.length)],
        salary: Math.floor(Math.random() * 100000) + 30000,
        joinDate: new Date(Date.now() - Math.random() * 5 * 365 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
        position: positions[Math.floor(Math.random() * positions.length)],
        manager: managers[Math.floor(Math.random() * managers.length)],
        location: locations[Math.floor(Math.random() * locations.length)],
        phone: `+1-${Math.floor(Math.random() * 900) + 100}-${Math.floor(Math.random() * 900) + 100}-${Math.floor(Math.random() * 9000) + 1000}`,
        status: statuses[Math.floor(Math.random() * statuses.length)],
        experience: Math.floor(Math.random() * 20) + 1,
        skills: skillSets[Math.floor(Math.random() * skillSets.length)],
        notes: `Notes for employee ${index + 1}. Lorem ipsum dolor sit amet, consectetur adipiscing elit.`
      }))
    }
  }
}
</script>

<style lang="scss" scoped>
.scroll-test-container {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.info-banner {
  margin-bottom: 20px;
  padding: 12px 16px;
  background: #fef3c7;
  border: 1px solid #f59e0b;
  border-radius: 8px;
  color: #92400e;
  font-size: 14px;
}

.scroll-controls {
  margin-bottom: 20px;
  padding: 16px;
  background: #f8fafc;
  border-radius: 8px;
  border: 1px solid #e5e7eb;
}

.control-group {
  display: flex;
  gap: 20px;
  align-items: center;
  
  label {
    display: flex;
    flex-direction: column;
    gap: 4px;
    font-size: 14px;
    color: #374151;
    
    input[type="range"] {
      width: 120px;
    }
    
    input[type="checkbox"] {
      width: auto;
    }
  }
}

.instructions {
  margin-bottom: 20px;
  padding: 16px;
  background: #f0f9ff;
  border-radius: 8px;
  border: 1px solid #bae6fd;
  
  h3 {
    color: #0369a1;
    margin-bottom: 12px;
  }
  
  ul {
    color: #1e40af;
    margin: 0;
    padding-left: 20px;
    
    li {
      margin-bottom: 4px;
    }
  }
}
</style>
