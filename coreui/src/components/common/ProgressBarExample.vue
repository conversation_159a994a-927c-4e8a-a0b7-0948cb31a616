<template>
  <div class="progress-bar-examples">
    <CCard>
      <CCardHeader>
        <CIcon name="cil-speedometer" />
        <strong>Enhanced Progress Bar Examples</strong>
      </CCardHeader>
      <CCardBody>
        <CRow>
          <!-- Basic Loading -->
          <CCol md="6" class="mb-3">
            <h6>Basic Loading</h6>
            <CButton 
              color="primary" 
              @click="showBasicLoading"
              class="mb-2"
            >
              Show Basic Loading
            </CButton>
          </CCol>

          <!-- Progress with Percentage -->
          <CCol md="6" class="mb-3">
            <h6>Progress with Percentage</h6>
            <CButton 
              color="success" 
              @click="showProgressWithPercentage"
              class="mb-2"
            >
              Show Progress Bar
            </CButton>
          </CCol>

          <!-- Custom Colors -->
          <CCol md="6" class="mb-3">
            <h6>Custom Colors</h6>
            <CButton 
              color="warning" 
              @click="showWarningProgress"
              class="mb-2 mr-2"
            >
              Warning
            </CButton>
            <CButton 
              color="danger" 
              @click="showDangerProgress"
              class="mb-2"
            >
              Danger
            </CButton>
          </CCol>

          <!-- Cancellable Progress -->
          <CCol md="6" class="mb-3">
            <h6>Cancellable Progress</h6>
            <CButton 
              color="info" 
              @click="showCancellableProgress"
              class="mb-2"
            >
              Show Cancellable
            </CButton>
          </CCol>
        </CRow>

        <!-- Usage Examples -->
        <hr class="my-4">
        <h5>Usage Examples</h5>
        
        <h6>Basic Usage:</h6>
        <pre class="bg-light p-3 rounded"><code>// Show basic loading
this.$refs.progressBar.open({
  title: 'Loading Data',
  message: 'Please wait while we fetch your data...'
});

// Hide progress
this.$refs.progressBar.close();</code></pre>

        <h6>Progress with Percentage:</h6>
        <pre class="bg-light p-3 rounded"><code>// Show progress with percentage
this.$refs.progressBar.open({
  title: 'Uploading Files',
  subtitle: 'Processing your documents',
  showPercentage: true,
  progress: 0,
  color: 'success'
});

// Update progress
this.$refs.progressBar.updateProgress(45, 'Uploading file 3 of 10...');</code></pre>

        <h6>Cancellable Progress:</h6>
        <pre class="bg-light p-3 rounded"><code>// Show cancellable progress
this.$refs.progressBar.open({
  title: 'Processing Data',
  message: 'This may take a few minutes...',
  showCancel: true,
  color: 'info'
}).then((result) => {
  if (result) {
    console.log('Completed successfully');
  } else {
    console.log('Cancelled by user');
  }
});</code></pre>
      </CCardBody>
    </CCard>

    <!-- Progress Bar Component Reference -->
    <ProgressBar ref="progressBar" />
  </div>
</template>

<script>
import ProgressBar from './ProgressBar.vue';

export default {
  name: 'ProgressBarExample',
  
  components: {
    ProgressBar
  },
  
  methods: {
    /**
     * Show basic loading dialog
     */
    showBasicLoading() {
      this.$refs.progressBar.open({
        title: 'Loading Data',
        subtitle: 'Fetching information from server',
        message: 'Please wait while we retrieve your data...',
        color: 'primary'
      });

      // Simulate loading time
      setTimeout(() => {
        this.$refs.progressBar.close();
      }, 3000);
    },

    /**
     * Show progress with percentage
     */
    showProgressWithPercentage() {
      this.$refs.progressBar.open({
        title: 'Uploading Files',
        subtitle: 'Processing your documents',
        showPercentage: true,
        progress: 0,
        color: 'success',
        message: 'Preparing upload...'
      });

      // Simulate progress updates
      let progress = 0;
      const interval = setInterval(() => {
        progress += Math.random() * 15;
        if (progress >= 100) {
          progress = 100;
          this.$refs.progressBar.updateProgress(progress, 'Upload completed!');
          setTimeout(() => {
            this.$refs.progressBar.close();
          }, 1000);
          clearInterval(interval);
        } else {
          const fileNumber = Math.floor(progress / 10) + 1;
          this.$refs.progressBar.updateProgress(
            progress, 
            `Uploading file ${fileNumber} of 10...`
          );
        }
      }, 500);
    },

    /**
     * Show warning progress
     */
    showWarningProgress() {
      this.$refs.progressBar.open({
        title: 'System Maintenance',
        subtitle: 'Updating system components',
        message: 'Please do not close this window...',
        color: 'warning',
        showPercentage: true,
        progress: 0
      });

      // Simulate progress
      let progress = 0;
      const interval = setInterval(() => {
        progress += 10;
        this.$refs.progressBar.updateProgress(progress);
        if (progress >= 100) {
          setTimeout(() => {
            this.$refs.progressBar.close();
          }, 500);
          clearInterval(interval);
        }
      }, 400);
    },

    /**
     * Show danger progress
     */
    showDangerProgress() {
      this.$refs.progressBar.open({
        title: 'Critical Update',
        subtitle: 'Installing security patches',
        message: 'System will restart automatically...',
        color: 'danger',
        theme: 'gradient'
      });

      setTimeout(() => {
        this.$refs.progressBar.close();
      }, 4000);
    },

    /**
     * Show cancellable progress
     */
    showCancellableProgress() {
      this.$refs.progressBar.open({
        title: 'Processing Large Dataset',
        subtitle: 'This operation may take several minutes',
        message: 'Analyzing 50,000 records...',
        showCancel: true,
        color: 'info',
        showPercentage: true,
        progress: 0
      }).then((result) => {
        if (result) {
          this.$toast.success('Processing completed successfully!');
        } else {
          this.$toast.info('Processing was cancelled by user');
        }
      });

      // Simulate long-running process
      let progress = 0;
      const interval = setInterval(() => {
        if (!this.$refs.progressBar.dialog) {
          clearInterval(interval);
          return;
        }
        
        progress += Math.random() * 5;
        if (progress >= 100) {
          progress = 100;
          this.$refs.progressBar.updateProgress(progress, 'Processing completed!');
          setTimeout(() => {
            this.$refs.progressBar.agree();
          }, 1000);
          clearInterval(interval);
        } else {
          const recordsProcessed = Math.floor((progress / 100) * 50000);
          this.$refs.progressBar.updateProgress(
            progress, 
            `Processed ${recordsProcessed.toLocaleString()} of 50,000 records...`
          );
        }
      }, 800);
    }
  }
};
</script>

<style lang="scss" scoped>
.progress-bar-examples {
  .bg-light {
    background-color: #f8f9fa !important;
  }
  
  pre {
    font-size: 13px;
    line-height: 1.4;
    
    code {
      color: #495057;
    }
  }
  
  .mb-2 {
    margin-bottom: 0.5rem !important;
  }
  
  .mr-2 {
    margin-right: 0.5rem !important;
  }
  
  .my-4 {
    margin-top: 1.5rem !important;
    margin-bottom: 1.5rem !important;
  }
}
</style>
